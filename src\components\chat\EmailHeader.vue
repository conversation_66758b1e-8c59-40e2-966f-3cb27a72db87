<template>
  <div class="h-12 bg-primary-200 flex justify-between items-center px-4 border-b border-primary-300">
    <!-- Left Actions -->
    <div class="flex gap-3 w-40">
      <button
        title="Archive"
        @click="$emit('archive')"
        class="text-secondary-600 hover:text-secondary-800 transition-colors duration-200 p-1 rounded-md hover:bg-primary-300"
      >
        <ArchiveBoxIcon class="w-5 h-5" />
      </button>
      <button
        title="Mark as unread"
        @click="$emit('markAsUnread')"
        class="text-secondary-600 hover:text-secondary-800 transition-colors duration-200 p-1 rounded-md hover:bg-primary-300"
      >
        <EnvelopeIcon class="w-5 h-5" />
      </button>
      <button
        v-if="canSnooze"
        title="Snooze"
        @click="$emit('snooze')"
        class="text-secondary-600 hover:text-secondary-800 transition-colors duration-200 p-1 rounded-md hover:bg-primary-300"
      >
        <ClockIcon class="w-5 h-5" />
      </button>
      <button
        title="Delete Email"
        @click="$emit('delete')"
        class="text-secondary-600 hover:text-red-500 transition-colors duration-200 p-1 rounded-md hover:bg-red-50"
      >
        <TrashIcon class="w-5 h-5" />
      </button>
    </div>

    <!-- Center Title -->
    <div class="flex-1 flex justify-center items-center relative">
      <div class="group cursor-pointer max-w-2xl">
        <h2
          :title="subject"
          class="text-xl font-semibold text-secondary-800 text-center truncate px-4"
        >
          {{ subject?.replace(/"/g, "") }}
        </h2>
        
        <!-- CC/BCC Dropdown -->
        <div
          v-if="cc || bcc"
          class="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 
                 opacity-0 group-hover:opacity-100 transition-opacity duration-300 
                 bg-primary-100 border border-primary-300 rounded-lg shadow-lg p-3 min-w-64 z-10"
        >
          <div class="space-y-2 text-sm">
            <div v-if="cc" class="flex gap-2">
              <span class="font-semibold text-secondary-700 min-w-8">Cc:</span>
              <div class="text-secondary-600">
                <div v-for="recipient in parsedCc" :key="recipient" class="truncate">
                  {{ recipient }}
                </div>
              </div>
            </div>
            <div v-if="bcc" class="flex gap-2">
              <span class="font-semibold text-secondary-700 min-w-8">Bcc:</span>
              <div class="text-secondary-600">
                <div v-for="recipient in parsedBcc" :key="recipient" class="truncate">
                  {{ recipient }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Close Button -->
    <div class="w-40 flex justify-end">
      <button 
        @click="$emit('close')" 
        class="p-2 text-secondary-600 hover:text-red-500 transition-colors duration-200 rounded-md hover:bg-red-50"
      >
        <XMarkIcon class="w-6 h-6" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  ArchiveBoxIcon,
  TrashIcon,
  EnvelopeIcon,
  XMarkIcon,
  ClockIcon,
} from "@heroicons/vue/24/outline"

interface Props {
  subject?: string
  cc?: string
  bcc?: string
  canSnooze?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  subject: '',
  cc: '',
  bcc: '',
  canSnooze: false
})

defineEmits<{
  archive: []
  markAsUnread: []
  snooze: []
  delete: []
  close: []
}>()

const parsedCc = computed(() => {
  if (!props.cc) return []
  try {
    return JSON.parse(props.cc)
  } catch {
    return [props.cc]
  }
})

const parsedBcc = computed(() => {
  if (!props.bcc) return []
  try {
    return JSON.parse(props.bcc)
  } catch {
    return [props.bcc]
  }
})
</script>
