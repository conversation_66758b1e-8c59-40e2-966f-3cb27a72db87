<template>
  <div class="flex items-start space-x-3 mb-4">
    <!-- Avatar -->
    <div
      class="w-12 h-12 rounded-full bg-gradient-to-br from-secondary-300 to-secondary-400 
             text-primary-100 text-sm font-semibold flex items-center justify-center 
             shadow-md border-2 border-primary-200"
      :title="message.from"
    >
      {{ getInitials(message.from) }}
    </div>

    <!-- Message Content -->
    <div class="flex-1 bg-primary-100 rounded-xl shadow-sm border border-primary-200 overflow-hidden">
      <!-- Message Header -->
      <div class="px-4 py-3 bg-primary-50 border-b border-primary-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <span class="font-semibold text-secondary-800 text-base">
              {{ getSenderName(message.from) }}
            </span>
            <button
              @click="toggleDetails"
              class="text-xs text-secondary-600 hover:text-secondary-800 
                     hover:underline transition-colors duration-200 px-2 py-1 
                     rounded-md hover:bg-primary-200"
            >
              {{ showDetails ? "Hide details" : "Show details" }}
            </button>
          </div>
          <div class="text-xs text-secondary-500 font-medium">
            {{ formatDate(message.date) }}
          </div>
        </div>

        <!-- Detailed Information -->
        <div v-if="showDetails" class="mt-3 space-y-2 text-sm border-t border-primary-200 pt-3">
          <div class="flex gap-3">
            <span class="font-medium text-secondary-700 min-w-12">From:</span>
            <span class="text-secondary-600">{{ getSenderEmail(message.from) }}</span>
          </div>
          <div v-if="message.to" class="flex gap-3">
            <span class="font-medium text-secondary-700 min-w-12">To:</span>
            <span 
              @click="toggleToExpanded" 
              class="text-secondary-600 cursor-pointer hover:text-secondary-800 transition-colors"
            >
              {{ getDisplayRecipients(message.to, showToExpanded) }}
            </span>
          </div>
          <div v-if="message.cc" class="flex gap-3">
            <span class="font-medium text-secondary-700 min-w-12">Cc:</span>
            <span 
              @click="toggleCcExpanded" 
              class="text-secondary-600 cursor-pointer hover:text-secondary-800 transition-colors"
            >
              {{ getDisplayRecipients(message.cc, showCcExpanded) }}
            </span>
          </div>
          <div v-if="message.bcc" class="flex gap-3">
            <span class="font-medium text-secondary-700 min-w-12">Bcc:</span>
            <span 
              @click="toggleBccExpanded" 
              class="text-secondary-600 cursor-pointer hover:text-secondary-800 transition-colors"
            >
              {{ getDisplayRecipients(message.bcc, showBccExpanded) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Message Body -->
      <div 
        class="px-4 py-4 text-secondary-800 leading-relaxed"
        v-html="message.body"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface EmailMessage {
  id?: string
  from: string
  to?: string
  cc?: string
  bcc?: string
  date: string | Date
  subject?: string
  body: string
}

interface Props {
  message: EmailMessage
}

defineProps<Props>()

const showDetails = ref(false)
const showToExpanded = ref(false)
const showCcExpanded = ref(false)
const showBccExpanded = ref(false)

const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

const toggleToExpanded = () => {
  showToExpanded.value = !showToExpanded.value
}

const toggleCcExpanded = () => {
  showCcExpanded.value = !showCcExpanded.value
}

const toggleBccExpanded = () => {
  showBccExpanded.value = !showBccExpanded.value
}

const getInitials = (email: string): string => {
  const nameMatch = email.match(/^(.*)<(.*)>$/)
  const name = nameMatch ? nameMatch[1].trim() : email.trim()
  
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('')
}

const getSenderName = (email: string): string => {
  const nameMatch = email.match(/^(.*)<(.*)>$/)
  return nameMatch ? nameMatch[1].trim() : email.split('@')[0]
}

const getSenderEmail = (email: string): string => {
  const emailMatch = email.match(/^(.*)<(.*)>$/)
  return emailMatch ? emailMatch[2].trim() : email
}

const getDisplayRecipients = (recipients: string, expanded: boolean): string => {
  if (!recipients) return ''
  
  const recipientList = recipients.split(',').map(r => r.trim())
  
  return recipientList
    .map(recipient => {
      const match = recipient.match(/^(.*)<(.*)>$/)
      if (!match) return recipient
      
      const name = match[1].trim()
      const email = match[2].trim()
      return expanded ? email : name
    })
    .join(', ')
}

const formatDate = (dateInput: string | Date): string => {
  const date = new Date(dateInput)
  return date.toLocaleString('en-US', {
    dateStyle: 'medium',
    timeStyle: 'short',
  })
}
</script>
