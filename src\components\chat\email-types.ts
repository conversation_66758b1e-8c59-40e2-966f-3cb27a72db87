export interface EmailMessage {
  id?: string
  from: string
  to?: string
  cc?: string
  bcc?: string
  date: string | Date
  subject?: string
  snippet?: string
  body: string
}

export interface EmailWithBody {
  id: string
  from: string
  to?: string
  cc?: string
  bcc?: string
  date: string | Date
  subject: string
  snippet: string
  body: string
}

export interface Draft {
  id: string
  parent_email_id: string
  to: string
  subject: string
  body: string
}

export interface MessageDisplayState {
  details: boolean
  to: boolean
  cc: boolean
  bcc: boolean
}

export type MessageDisplayStates = Map<number, MessageDisplayState>

// Helper function to create default display state
export function createDefaultDisplayState(): MessageDisplayState {
  return {
    details: false,
    to: false,
    cc: false,
    bcc: false
  }
}

// Helper function to toggle a field in display state
export function toggleDisplayField(
  states: MessageDisplayStates, 
  index: number, 
  field: keyof MessageDisplayState
): void {
  if (!states.has(index)) {
    states.set(index, createDefaultDisplayState())
  }
  const state = states.get(index)!
  state[field] = !state[field]
}

// Helper function to get display state for a message
export function getDisplayState(
  states: MessageDisplayStates, 
  index: number
): MessageDisplayState {
  return states.get(index) || createDefaultDisplayState()
}
